declare namespace Api.ShiftScheduleList {
  export interface Request {
    /**
     * 页码
     */
    page?: number
    /**
     * 每页数量
     */
    page_size?: number
    /**
     * 排班表名称
     */
    schedule_name?: string
    /**
     * 排班类型
     */
    schedule_type?: number
    /**
     * 状态
     */
    status?: number
    /**
     * 织厂id
     */
    weaving_factory_id?: number
    [property: string]: any
  }
  /**
   * basic_data.GetWeavingFactoryScheduleData
   */
  export interface Response {
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 允许提前打卡时间(分钟)
     */
    early_clock_in_minutes?: number
    /**
     * 结束时间
     */
    end_time?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 备注
     */
    remark?: string
    /**
     * 排班表名称
     */
    schedule_name?: string
    /**
     * 开始时间
     */
    start_time?: string
    /**
     * 状态
     */
    status?: number
    /**
     * 状态名称
     */
    status_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 织厂id
     */
    weaving_factory_id?: number
    /**
     * 织厂名称
     */
    weaving_factory_name?: string
    [property: string]: any
  }
}
declare namespace Api.DeleteShiftScheduleApi{
  /**
   * basic_data.DeleteWeavingFactoryScheduleParam
   */
  export interface Request {
    delete_remark?: string
    id?: string
    [property: string]: any
  }
  /**
   * basic_data.DeleteWeavingFactoryScheduleData
   */
  export interface Response {
    id?: number[]
    [property: string]: any
  }
}
declare namespace Api.DisableShiftScheduleApi{
  /**
   * basic_data.UpdateWeavingFactoryScheduleStatusParam
   */
  export interface Request {
    id?: string
    status?: number
    [property: string]: any
  }
  export interface Response {

  }
}
declare namespace Api.PostShiftScheduleAddApi{
  /**
   * basic_data.AddWeavingFactoryScheduleParam
   */
  export interface Request {
    /**
     * 允许提前打卡时间(分钟)
     */
    early_clock_in_minutes?: number
    /**
     * 结束时间
     */
    end_time?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 排班表名称
     */
    schedule_name?: string
    /**
     * 开始时间
     */
    start_time?: string
    /**
     * 织厂id
     */
    weaving_factory_id?: number
    [property: string]: any
  }
  /**
   * basic_data.AddWeavingFactoryScheduleData
   */
  export interface Response {
    id?: number
    [property: string]: any
  }
}
declare namespace Api.PutShiftScheduleUpdateApi {
  /**
   * basic_data.UpdateWeavingFactoryScheduleParam
   */
  export interface Request {
    /**
     * 允许提前打卡时间(分钟)
     */
    early_clock_in_minutes?: number
    /**
     * 结束时间
     */
    end_time?: string
    id?: number
    /**
     * 备注
     */
    remark?: string
    /**
     * 排班表名称
     */
    schedule_name?: string
    /**
     * 开始时间
     */
    start_time?: string
    /**
     * 织厂id
     */
    weaving_factory_id?: number
    [property: string]: any
  }
  /**
   * basic_data.UpdateWeavingFactoryScheduleData
   */
  export interface Response {
    id?: number
    [property: string]: any
  }
}
declare namespace Api.GetShiftScheduleDetailApi {
  /**
   * basic_data.GetWeavingFactoryScheduleData
   */
  export interface Response {
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 允许提前打卡时间(分钟)
     */
    early_clock_in_minutes?: number
    /**
     * 结束时间
     */
    end_time?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 备注
     */
    remark?: string
    /**
     * 排班表名称
     */
    schedule_name?: string
    /**
     * 开始时间
     */
    start_time?: string
    /**
     * 状态
     */
    status?: number
    /**
     * 状态名称
     */
    status_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 织厂id
     */
    weaving_factory_id?: number
    /**
     * 织厂名称
     */
    weaving_factory_name?: string
    [property: string]: any
  }
  export interface Request {
    /**
     * id
     */
    id: number
    [property: string]: any
  }
}
