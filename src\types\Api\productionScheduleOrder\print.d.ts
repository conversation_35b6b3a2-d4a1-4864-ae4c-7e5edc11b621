declare namespace Api.PrintFineCode {
  /**
   * produce.PrintProductionScheduleOrderFineCodeParam
   */
  export interface Request {
    /**
     * 细码ID列表，逗号分隔
     */
    ids?: string
    /**
     * 排产单ID
     */
    production_schedule_order_id?: number
    /**
     * 备注
     */
    remark?: string
    [property: string]: any
  }
  /**
   * produce.PrintProductionScheduleOrderFineCodeResponse
   */
  export interface Response {
    /**
     * 细码详情列表
     */
    fine_code_list?: ProduceProductionScheduleOrderFineCodePrintItem[]
    /**
     * 打印数量
     */
    print_count?: number
    /**
     * 打印记录ID
     */
    print_record_id?: number
    [property: string]: any
  }

  /**
   * produce.ProductionScheduleOrderFineCodePrintItem
   */
  export interface ProduceProductionScheduleOrderFineCodePrintItem {
    /**
     * 款号
     */
    account_num?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 客户id
     */
    customer_id?: number
    /**
     * 客户名称
     */
    customer_name?: string
    /**
     * 布种后整
     */
    fabric_finishing?: string
    /**
     * 条形码
     */
    fabric_piece_code?: string
    /**
     * 坯布编号
     */
    grey_fabric_code?: string
    /**
     * 坯布颜色id
     */
    grey_fabric_color_id?: number
    /**
     * 坯布颜色名称
     */
    grey_fabric_color_name?: string
    /**
     * 坯布克重
     */
    grey_fabric_gram_weight?: string
    /**
     * 坯布克重及单位名称
     */
    grey_fabric_gram_weight_and_unit_name?: string
    /**
     * 坯布克重单位id(字典)
     */
    grey_fabric_gram_weight_unit_id?: number
    /**
     * 坯布克重单位名称
     */
    grey_fabric_gram_weight_unit_name?: string
    /**
     * 坯布id
     */
    grey_fabric_id?: number
    /**
     * 坯布等级ID
     */
    grey_fabric_level_id?: number
    /**
     * 坯布等级名称
     */
    grey_fabric_level_name?: string
    /**
     * 坯布名称
     */
    grey_fabric_name?: string
    /**
     * 坯布幅宽
     */
    grey_fabric_width?: string
    /**
     * 坯布幅宽及单位名称
     */
    grey_fabric_width_and_unit_name?: string
    /**
     * 坯布幅宽单位id(字典)
     */
    grey_fabric_width_unit_id?: number
    /**
     * 坯布幅宽单位名称
     */
    grey_fabric_width_unit_name?: string
    /**
     * 细码ID
     */
    id?: number
    /**
     * 验布条数
     */
    inspection_roll?: number
    /**
     * 查布时间
     */
    inspection_time?: string
    /**
     * 验布数量
     */
    inspection_weight?: number
    /**
     * 查布人ID
     */
    inspector_id?: number
    /**
     * 查布人姓名
     */
    inspector_name?: string
    /**
     * 机台号
     */
    machine_number?: string
    /**
     * 机台号
     */
    machine_number_id?: number
    /**
     * 用料批号（子表所有的原料批号组合取来用+号拼接，不用去重）
     */
    material_batch_numbers?: string
    /**
     * 针寸数
     */
    needle_size?: string
    /**
     * 打印时间
     */
    print_time?: string
    /**
     * 打印人ID
     */
    printer_id?: number
    /**
     * 打印人姓名
     */
    printer_name?: string
    /**
     * 生产通知单ID
     */
    production_notify_order_id?: number
    /**
     * 生产通知单号
     */
    production_notify_order_no?: string
    /**
     * 排产单ID
     */
    production_schedule_order_id?: number
    /**
     * 质量备注
     */
    quality_remark?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 总针数
     */
    total_needle_size?: string
    /**
     * 布种类型编号
     */
    type_grey_fabric_code?: string
    /**
     * 布种类型id
     */
    type_grey_fabric_id?: number
    /**
     * 布种类型名称
     */
    type_grey_fabric_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 卷号
     */
    volume_number?: number
    /**
     * 织厂id
     */
    weave_factory_id?: number
    /**
     * 织厂名称
     */
    weave_factory_name?: string
    /**
     * 织造规格
     */
    weaving_specifications?: string
    /**
     * 纱批
     */
    yarn_batch?: string
    /**
     * 纱牌（子表所有的原料品牌组合取来用+号拼接，不用去重）
     */
    yarn_brand?: string
    /**
     * 纱名（子表的原料名称、原料品牌、原料批号、原料缸号、原料颜色组合起来，每个子表间数据用"+"分开）
     */
    yarn_name?: string
    [property: string]: any
  }
}
