<template>
  <div class="shift-schedule-add p-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/shiftScheduleManagement/schedule' }">排班管理</el-breadcrumb-item>
        <el-breadcrumb-item>新增排班</el-breadcrumb-item>
      </el-breadcrumb>
      <h1 class="text-2xl font-bold text-gray-800 mt-4">新增排班</h1>
    </div>

    <!-- 表单区域 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="max-w-2xl"
      >


        <el-form-item label="班次" prop="shiftType">
          <el-radio-group v-model="form.shiftType" @change="handleShiftTypeChange">
            <el-radio value="morning">
              <span class="flex items-center">
                <el-icon class="mr-1"><Sunrise /></el-icon>
                早班
              </span>
            </el-radio>
            <el-radio value="evening">
              <span class="flex items-center">
                <el-icon class="mr-1"><Moon /></el-icon>
                晚班
              </span>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="工作时间">
          <div class="flex items-center space-x-4">
            <el-form-item prop="startTime" class="mb-0">
              <el-time-picker
                v-model="form.startTime"
                placeholder="开始时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 150px"
              />
            </el-form-item>
            <span class="text-gray-500">至</span>
            <el-form-item prop="endTime" class="mb-0">
              <el-time-picker
                v-model="form.endTime"
                placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 150px"
              />
            </el-form-item>
            <span class="text-sm text-gray-500">
              工作时长: {{ workDuration }}
            </span>
          </div>
        </el-form-item>

        <el-form-item label="工作地点" prop="workLocation">
          <el-input
            v-model="form.workLocation"
            placeholder="请输入工作地点"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="负责人" prop="supervisor">
          <el-select
            v-model="form.supervisor"
            placeholder="请选择负责人"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="supervisor in supervisorList"
              :key="supervisor.id"
              :label="supervisor.name"
              :value="supervisor.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入备注信息"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <div class="flex space-x-4">
            <el-button type="primary" @click="handleSubmit" :loading="loading">
              <el-icon><Check /></el-icon>
              保存
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button @click="handleCancel">
              <el-icon><Close /></el-icon>
              取消
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Sunrise, Moon, Check, Refresh, Close } from '@element-plus/icons-vue'
import { PostShiftScheduleAddApi } from '@/api/shiftSchedule'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const formRef = ref()
const supervisorList = ref([])

// 表单数据
const form = reactive({
  shiftType: 'morning',
  startTime: '08:00',
  endTime: '17:00',
  workLocation: '',
  supervisor: '',
  remark: ''
})

// 表单验证规则
const rules = {
  shiftType: [{ required: true, message: '请选择班次', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  workLocation: [{ required: true, message: '请输入工作地点', trigger: 'blur' }]
}

// 计算工作时长
const workDuration = computed(() => {
  if (!form.startTime || !form.endTime) return ''
  
  const start = new Date(`2000-01-01 ${form.startTime}`)
  const end = new Date(`2000-01-01 ${form.endTime}`)
  
  if (end <= start) {
    // 跨天情况
    end.setDate(end.getDate() + 1)
  }
  
  const diff = end.getTime() - start.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
})

// 获取负责人列表
const getSupervisorList = async () => {
  try {
    // 这里可以调用获取负责人列表的API
    // 暂时使用模拟数据
    supervisorList.value = [
      { id: '1', name: '张主管' },
      { id: '2', name: '李经理' },
      { id: '3', name: '王组长' }
    ]
  } catch (error) {
    console.error('获取负责人列表失败:', error)
    ElMessage.error('获取负责人列表失败')
  }
}

// 班次类型变化
const handleShiftTypeChange = (shiftType: string) => {
  // 根据班次类型设置默认时间
  if (shiftType === 'morning') {
    form.startTime = '08:00'
    form.endTime = '17:00'
  } else if (shiftType === 'evening') {
    form.startTime = '18:00'
    form.endTime = '02:00'
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    const { request } = PostShiftScheduleAddApi()
    const response = await request(form)
    
    if (response.code === 200) {
      ElMessage.success('新增排班成功')
      router.push('/shiftScheduleManagement/schedule')
    } else {
      ElMessage.error(response.message || '新增排班失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    shiftType: 'morning',
    startTime: '08:00',
    endTime: '17:00',
    workLocation: '',
    supervisor: '',
    remark: ''
  })
}

// 取消
const handleCancel = () => {
  router.push('/shiftScheduleManagement/schedule')
}

// 组件挂载
onMounted(() => {
  getSupervisorList()
})
</script>

<style scoped>
.shift-schedule-add {
  min-height: 100vh;
  background-color: #f5f5f5;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-radio) {
  margin-right: 24px;
}
</style>