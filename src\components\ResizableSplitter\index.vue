<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 定义组件的 Props 类型
interface ResizableSplitterProps {
  /** 分割方向 */
  direction?: 'horizontal' | 'vertical'
  /** 初始分割比例（百分比） */
  initialSplit?: number
  /** 最小左侧/上侧面板大小（百分比） */
  minSize?: number
  /** 最大左侧/上侧面板大小（百分比） */
  maxSize?: number
  /** 分割器宽度/高度（像素） */
  splitterSize?: number
  /** 是否禁用拖拽 */
  disabled?: boolean
  /** 分割器样式类名 */
  splitterClass?: string
  /** 是否显示分割线 */
  showLine?: boolean
  /** 分割线样式 */
  lineStyle?: Record<string, any>
}

// 定义 Props
const props = withDefaults(defineProps<ResizableSplitterProps>(), {
  direction: 'horizontal',
  initialSplit: 50,
  minSize: 10,
  maxSize: 90,
  splitterSize: 8,
  disabled: false,
  splitterClass: '',
  showLine: true,
  lineStyle: () => ({}),
})

// 定义 Emits
const emits = defineEmits<{
  'split-change': [split: number]
  'drag-start': [split: number]
  'drag-end': [split: number]
}>()

// 响应式数据
const splitRatio = ref(props.initialSplit)
const isDragging = ref(false)
const startPosition = ref(0)
const startSplit = ref(50)
const containerRef = ref<HTMLElement>()

// 计算属性
const isHorizontal = computed(() => props.direction === 'horizontal')

const splitterStyle = computed(() => {
  const baseStyle: Record<string, any> = {
    position: 'relative',
    flexShrink: 0,
    cursor: props.disabled ? 'default' : (isHorizontal.value ? 'col-resize' : 'row-resize'),
    userSelect: 'none',
    zIndex: 10,
  }

  if (isHorizontal.value) {
    baseStyle.width = `${props.splitterSize}px`
    baseStyle.height = '100%'
  } else {
    baseStyle.width = '100%'
    baseStyle.height = `${props.splitterSize}px`
  }

  return baseStyle
})

const lineStyle = computed(() => {
  const defaultStyle: Record<string, any> = {
    position: 'absolute',
    backgroundColor: '#e0e0e0',
    borderRadius: '2px',
    transition: isDragging.value ? 'none' : 'all 0.2s ease',
  }

  if (isHorizontal.value) {
    defaultStyle.width = '2px'
    defaultStyle.height = '60px'
    defaultStyle.left = '50%'
    defaultStyle.top = '50%'
    defaultStyle.transform = 'translate(-50%, -50%)'
  } else {
    defaultStyle.width = '60px'
    defaultStyle.height = '2px'
    defaultStyle.left = '50%'
    defaultStyle.top = '50%'
    defaultStyle.transform = 'translate(-50%, -50%)'
  }

  return { ...defaultStyle, ...props.lineStyle }
})

const leftPanelStyle = computed(() => {
  if (isHorizontal.value) {
    return { width: `${splitRatio.value}%`, height: '100%' }
  } else {
    return { width: '100%', height: `${splitRatio.value}%` }
  }
})

const rightPanelStyle = computed(() => {
  if (isHorizontal.value) {
    return { width: `${100 - splitRatio.value}%`, height: '100%' }
  } else {
    return { width: '100%', height: `${100 - splitRatio.value}%` }
  }
})

// 拖拽处理函数
function startDrag(clientPosition: number) {
  if (props.disabled) return
  
  isDragging.value = true
  startPosition.value = clientPosition
  startSplit.value = splitRatio.value
  
  document.body.style.cursor = isHorizontal.value ? 'col-resize' : 'row-resize'
  document.body.style.userSelect = 'none'
  
  emits('drag-start', splitRatio.value)
}

function updateDrag(clientPosition: number) {
  if (!isDragging.value || !containerRef.value) return

  const containerRect = containerRef.value.getBoundingClientRect()
  const containerSize = isHorizontal.value ? containerRect.width : containerRect.height
  const delta = clientPosition - startPosition.value
  const deltaPercent = (delta / containerSize) * 100

  let newSplit = startSplit.value + deltaPercent
  newSplit = Math.max(props.minSize, Math.min(props.maxSize, newSplit))
  
  splitRatio.value = newSplit
  emits('split-change', newSplit)
}

function stopDrag() {
  if (!isDragging.value) return
  
  isDragging.value = false
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
  
  emits('drag-end', splitRatio.value)
}

// 鼠标事件处理
function handleMouseDown(event: MouseEvent) {
  event.preventDefault()
  event.stopPropagation()
  
  const clientPosition = isHorizontal.value ? event.clientX : event.clientY
  startDrag(clientPosition)
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

function handleMouseMove(event: MouseEvent) {
  const clientPosition = isHorizontal.value ? event.clientX : event.clientY
  updateDrag(clientPosition)
}

function handleMouseUp() {
  stopDrag()
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

// 触摸事件处理
function handleTouchStart(event: TouchEvent) {
  event.preventDefault()
  event.stopPropagation()
  
  const touch = event.touches[0]
  const clientPosition = isHorizontal.value ? touch.clientX : touch.clientY
  startDrag(clientPosition)
  
  document.addEventListener('touchmove', handleTouchMove, { passive: false })
  document.addEventListener('touchend', handleTouchEnd)
}

function handleTouchMove(event: TouchEvent) {
  event.preventDefault()
  const touch = event.touches[0]
  const clientPosition = isHorizontal.value ? touch.clientX : touch.clientY
  updateDrag(clientPosition)
}

function handleTouchEnd() {
  stopDrag()
  document.removeEventListener('touchmove', handleTouchMove)
  document.removeEventListener('touchend', handleTouchEnd)
}

// 公开方法
function setSplit(split: number) {
  const newSplit = Math.max(props.minSize, Math.min(props.maxSize, split))
  splitRatio.value = newSplit
  emits('split-change', newSplit)
}

function getSplit(): number {
  return splitRatio.value
}

// 组件挂载时的处理
onMounted(() => {
  splitRatio.value = props.initialSplit
})

// 组件卸载时清理
onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.removeEventListener('touchmove', handleTouchMove)
  document.removeEventListener('touchend', handleTouchEnd)
})

// 暴露方法给父组件
defineExpose({
  setSplit,
  getSplit,
})
</script>

<template>
  <div
    ref="containerRef"
    class="resizable-splitter-container"
    :class="{ 'horizontal': isHorizontal, 'vertical': !isHorizontal }"
  >
    <!-- 左侧/上侧面板 -->
    <div
      class="panel left-panel"
      :style="leftPanelStyle"
    >
      <slot name="left" />
      <slot name="top" />
    </div>

    <!-- 分割器 -->
    <div
      class="splitter"
      :class="[
        splitterClass,
        {
          'dragging': isDragging,
          'disabled': disabled,
          'horizontal': isHorizontal,
          'vertical': !isHorizontal,
        }
      ]"
      :style="splitterStyle"
      :title="disabled ? '' : (isHorizontal ? '拖拽调整面板大小' : '拖拽调整面板大小')"
      @mousedown="handleMouseDown"
      @touchstart="handleTouchStart"
    >
      <!-- 分割线 -->
      <div
        v-if="showLine"
        class="splitter-line"
        :style="lineStyle"
      />
      
      <!-- 自定义分割器内容 -->
      <slot name="splitter" />
    </div>

    <!-- 右侧/下侧面板 -->
    <div
      class="panel right-panel"
      :style="rightPanelStyle"
    >
      <slot name="right" />
      <slot name="bottom" />
    </div>
  </div>
</template>

<style scoped>
.resizable-splitter-container {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.resizable-splitter-container.horizontal {
  flex-direction: row;
}

.resizable-splitter-container.vertical {
  flex-direction: column;
}

.panel {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.splitter {
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.splitter:hover:not(.disabled) {
  background: linear-gradient(180deg, #e9ecef 0%, #dee2e6 100%);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
}

.splitter.dragging {
  background: linear-gradient(180deg, #dee2e6 0%, #ced4da 100%);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

.splitter.disabled {
  cursor: default !important;
  opacity: 0.5;
}

.splitter-line {
  background-color: #adb5bd;
  transition: all 0.2s ease;
}

.splitter:hover:not(.disabled) .splitter-line {
  background-color: #6c757d;
}

.splitter.dragging .splitter-line {
  background-color: #495057;
}

/* 响应式适配 */
@media screen and (max-width: 768px) {
  .splitter {
    min-width: 12px;
    min-height: 12px;
  }
  
  .splitter.horizontal {
    min-width: 12px;
  }
  
  .splitter.vertical {
    min-height: 12px;
  }
}

@media screen and (min-width: 1920px) {
  .splitter {
    min-width: 14px;
    min-height: 14px;
  }
  
  .splitter-line {
    width: 4px !important;
    height: 80px !important;
  }
  
  .splitter.vertical .splitter-line {
    width: 80px !important;
    height: 4px !important;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .splitter {
    background: linear-gradient(180deg, #343a40 0%, #495057 100%);
  }
  
  .splitter:hover:not(.disabled) {
    background: linear-gradient(180deg, #495057 0%, #6c757d 100%);
  }
  
  .splitter-line {
    background-color: #6c757d;
  }
  
  .splitter:hover:not(.disabled) .splitter-line {
    background-color: #adb5bd;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .splitter {
    border: 2px solid currentColor;
  }
  
  .splitter-line {
    background-color: currentColor;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .splitter,
  .splitter-line {
    transition: none !important;
  }
}
</style>
