import { PrintFineCode } from './scheduleProduction'
import { GetPurchaseGreyFarbricById } from '@/api/blanketManagement'
import { dyeing_and_finishingdetail } from '@/api/dyeingNotice'
import { FinishProductDetail } from '@/api/finishedProductProcurement'
import { getGfmSaleDeliveryOrder } from '@/api/greyFabricSales'
import { getProductionNotifyOrder } from '@/api/productionNotice'
import { getSaleProductPlanOrder } from '@/api/productSalePlan'
import { SaleOrderDetail } from '@/api/rawMaterIalSaleOrder'
import { PurchaseOrderRawMaterialDetail } from '@/api/rawMaterialSourcing'
import { getWeightItemList, updateget } from '@/api/saleDeliver'
import { getSaleTransferOrder } from '@/api/transferSales'
import { raw_process_orderoutputdetail } from '@/api/rawdyeingNotice'
import { raw_material_colordetail } from '@/api/rawInformationColor'
import { GetQualityCheckDetail } from '@/api'
import { FpmQualityCheckoutReportPrint } from '@/api/qualityInspectionReport'
import { getCustomerReconciliationListNoPage } from '@/api/customerReconciliation'
import { GetSupplierReconciliationListNoPage } from '@/api/supplierStatement'

export default {
  PurchaseOrderRawMaterialDetail,
  GetPurchaseGreyFarbricById,
  FinishProductDetail,
  getProductionNotifyOrder,
  dyeing_and_finishingdetail,
  getSaleProductPlanOrder,
  getWeightItemList,
  updateget,
  getGfmSaleDeliveryOrder,
  SaleOrderDetail,
  getSaleTransferOrder,
  raw_process_orderoutputdetail,
  raw_material_colordetail, // 原料资料标签打印
  GetQualityCheckDetail, // 质检单打印
  FpmQualityCheckoutReportPrint, // 质检单报告打印
  getCustomerReconciliationListNoPage, // 客户对账表
  GetSupplierReconciliationListNoPage, // 供方对账表
  PrintFineCode, // 生产排产单打印
}
