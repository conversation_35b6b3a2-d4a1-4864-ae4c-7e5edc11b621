<template>
  <div class="list-page">
    <!-- 页面标题 -->
    <!-- <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800">排班管理</h1>
      <p class="text-gray-600 mt-2">管理早班和晚班的排班安排</p>
    </div> -->

    <!-- 操作栏 -->
    <FildCard title="" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="班次:">
          <el-select
            v-model="searchForm.shiftType"
            placeholder="选择班次"
            clearable
            @change="handleSearch"
          >
            <el-option label="早班" value="morning" />
            <el-option label="晚班" value="evening" />
          </el-select>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增排班
          </el-button>
          <el-button @click="handleBatchAdd">
            <el-icon><DocumentAdd /></el-icon>
            批量排班
          </el-button>
          <!-- <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button> -->
        </DescriptionsFormItem>
      </div>
    </FildCard>

    <!-- 排班表格 -->
    <FildCard title="排班列表" class="table-card-full" :tool-bar="false">
      <Table
        :config="tableConfig"
        :table-list="tableData"
        :column-list="columnList"
      >
        <template #shiftType="{ row }">
          <el-tag :type="row.shiftType === 'morning' ? 'success' : 'warning'">
            {{ row.shiftType === 'morning' ? '早班' : '晚班' }}
          </el-tag>
        </template>
        <template #status="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
        <template #operate="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="info" link @click="handleDetail(row)">
            详情
          </el-button>
          <el-button type="danger" link @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </Table>
    </FildCard>

    <!-- 新增/编辑对话框 -->
    <vxe-modal
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      height="auto"
      :mask="true"
      :lock-view="true"
      :esc-closable="true"
      :resize="false"
      @close="handleDialogClose"
    >
      <template #default>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="班次" prop="shiftType">
            <el-radio-group v-model="form.shiftType">
              <el-radio value="morning">早班</el-radio>
              <el-radio value="evening">晚班</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="开始时间" prop="startTime">
            <el-time-picker
              v-model="form.startTime"
              placeholder="选择开始时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="结束时间" prop="endTime">
            <el-time-picker
              v-model="form.endTime"
              placeholder="选择结束时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, DocumentAdd, Download, Search } from '@element-plus/icons-vue'
import Table from '@/components/Table.vue'
import FildCard from '@/components/FildCard.vue'
import {
  GetShiftScheduleListApi,
  GetShiftScheduleListApiExport,
  PostShiftScheduleAddApi,
  PutShiftScheduleUpdateApi,
  DeleteShiftScheduleApi
} from '@/api/shiftSchedule'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  shiftType: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 表格配置
const tableConfig = reactive({
  loading: false,
  page: 1,
  size: 20,
  total: 0,
  showPagition: true,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '200',
  height: 'auto',
  handleSelectionChange: (params: any) => {
    selectedRows.value = params.selectCheck
  }
})

// 表格列配置
const columnList = ref([
  {
    field: 'shiftType',
    title: '班次',
    width: '100',
    soltName: 'shiftType'
  },
  {
    field: 'startTime',
    title: '开始时间',
    width: '120'
  },
  {
    field: 'endTime',
    title: '结束时间',
    width: '120'
  },
  {
    field: 'status',
    title: '状态',
    width: '100',
    soltName: 'status'
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: '150'
  },
  {
    field: 'createTime',
    title: '创建时间',
    width: '160'
  },
  {
    field: 'operate',
    title: '操作',
    width: '200',
    fixed: 'right',
    soltName: 'operate'
  }
])

// 表单数据
const form = reactive({
  id: null,
  shiftType: 'morning',
  startTime: '08:00',
  endTime: '17:00',
  remark: ''
})

// 表单验证规则
const rules = {
  shiftType: [{ required: true, message: '请选择班次', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
}

// 计算属性
const dialogTitle = computed(() => {
  return form.id ? '编辑排班' : '新增排班'
})

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    completed: 'warning'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '正常',
    inactive: '停用',
    completed: '已完成'
  }
  return statusMap[status] || '未知'
}

// 获取排班列表
const getShiftScheduleList = async () => {
  loading.value = true
  tableConfig.loading = true
  try {
    const { request } = GetShiftScheduleListApi()
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    const response = await request(params)
    if (response.code === 200) {
      tableData.value = response.data.list || []
      pagination.total = response.data.total || 0
      tableConfig.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取排班列表失败:', error)
    ElMessage.error('获取排班列表失败')
  } finally {
    loading.value = false
    tableConfig.loading = false
  }
}



// 搜索
const handleSearch = () => {
  pagination.page = 1
  getShiftScheduleList()
}

// 新增
const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

// 批量新增
const handleBatchAdd = () => {
  // TODO: 实现批量排班功能
  ElMessage.info('批量排班功能开发中')
}

// 导出
const handleExport = async () => {
  try {
    const { request } = GetShiftScheduleListApiExport({ nameFile: '排班列表' })
    await request(searchForm)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 编辑
const handleEdit = (row: any) => {
  Object.assign(form, row)
  dialogVisible.value = true
}

// 详情
const handleDetail = (row: any) => {
  // TODO: 实现详情页面
  ElMessage.info('详情页面开发中')
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条排班记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const { request } = DeleteShiftScheduleApi()
    const response = await request({ id: row.id })
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getShiftScheduleList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  getShiftScheduleList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  getShiftScheduleList()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    const api = form.id ? PutShiftScheduleUpdateApi() : PostShiftScheduleAddApi()
    const response = await api.request(form)
    
    if (response.code === 200) {
      ElMessage.success(form.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getShiftScheduleList()
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

// 对话框关闭
const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: null,
    shiftType: 'morning',
    startTime: '08:00',
    endTime: '17:00',
    remark: ''
  })
}

// 组件挂载
onMounted(() => {
  getShiftScheduleList()
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
