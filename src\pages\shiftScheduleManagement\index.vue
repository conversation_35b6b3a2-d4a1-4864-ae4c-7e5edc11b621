<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DocumentAdd, Download, Plus, Search } from '@element-plus/icons-vue'
import Table from '@/components/Table.vue'
import FildCard from '@/components/FildCard.vue'
import {
  DeleteShiftScheduleApi,
  GetShiftScheduleDetailApi,
  GetShiftScheduleListApi,
  PostShiftScheduleAddApi,
  PutShiftScheduleUpdateApi,
} from '@/api/shiftSchedule'
import { GetBusinessUnitListApi } from '@/api/treeApi'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { BusinessUnitIdEnum } from '@/common/enum'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const tableData = ref<Api.ShiftScheduleList.Response[]>([])
const selectedRows = ref([])
const formRef = ref()

// 搜索表单
const searchForm = reactive<Api.ShiftScheduleList.Request>({
  schedule_name: '',
  weaving_factory_id: undefined,
  status: undefined,
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
})

// 表格配置
const tableConfig = reactive({
  loading: false,
  page: 1,
  size: 20,
  total: 0,
  showPagition: true,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '200',
  height: 'auto',
  handleSelectionChange: (params: any) => {
    selectedRows.value = params.selectCheck
  },
  handleSizeChange: (size: number) => {
    pagination.pageSize = size
    pagination.page = 1
    getShiftScheduleList()
  },
  handleCurrentChange: (page: number) => {
    pagination.page = page
    getShiftScheduleList()
  },
})

// 表格列配置
const columnList = ref([
  {
    field: 'schedule_name',
    title: '排班表名称',
    width: '150',
  },
  {
    field: 'weaving_factory_name',
    title: '织厂名称',
    width: '150',
  },
  {
    field: 'start_time',
    title: '开始时间',
    width: '120',
  },
  {
    field: 'end_time',
    title: '结束时间',
    width: '120',
  },
  {
    field: 'early_clock_in_minutes',
    title: '允许提前打卡(分钟)',
    width: '150',
  },
  {
    field: 'status',
    title: '状态',
    width: '100',
    soltName: 'status',
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 150,
  },
  {
    field: 'creator_name',
    title: '创建人',
    width: '120',
  },
  {
    field: 'create_time',
    title: '创建时间',
    width: '160',
    isDate: true,
  },
])

// 表单数据
const form = reactive<Api.PostShiftScheduleAddApi.Request & { id?: number }>({
  id: undefined,
  schedule_name: '',
  weaving_factory_id: undefined,
  start_time: '08:00',
  end_time: '17:00',
  early_clock_in_minutes: 0,
  remark: '',
})

// 表单验证规则
const rules = {
  schedule_name: [{ required: true, message: '请输入排班表名称', trigger: 'blur' }],
  weaving_factory_id: [{ required: true, message: '请选择织厂', trigger: 'change' }],
  start_time: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  end_time: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  early_clock_in_minutes: [{ required: true, message: '请输入允许提前打卡时间', trigger: 'blur' }],
}

// 计算属性
const dialogTitle = computed(() => {
  return form.id ? '编辑排班' : '新增排班'
})

// 获取排班列表
async function getShiftScheduleList() {
  loading.value = true
  tableConfig.loading = true
  try {
    const api = GetShiftScheduleListApi()
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...searchForm,
    }
    await api.fetchData(params)
    if (api.success.value) {
      tableData.value = api.data.value.list || []
      pagination.total = api.data.value.total || 0
      tableConfig.total = api.data.value.total || 0
      tableConfig.page = pagination.page
      tableConfig.size = pagination.pageSize
    }
    else {
      ElMessage.error(api.msg.value || '获取排班列表失败')
    }
  }
  catch (error) {
    console.error('获取排班列表失败:', error)
    ElMessage.error('获取排班列表失败')
  }
  finally {
    loading.value = false
    tableConfig.loading = false
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1
  getShiftScheduleList()
}

// 新增
function handleAdd() {
  resetForm()
  dialogVisible.value = true
}

// 批量新增
// function handleBatchAdd() {
//   // TODO: 实现批量排班功能
//   ElMessage.info('批量排班功能开发中')
// }

// 编辑
function handleEdit(row: Api.ShiftScheduleList.Response) {
  Object.assign(form, {
    id: row.id,
    schedule_name: row.schedule_name,
    weaving_factory_id: row.weaving_factory_id,
    start_time: row.start_time,
    end_time: row.end_time,
    early_clock_in_minutes: row.early_clock_in_minutes,
    remark: row.remark,
  })
  dialogVisible.value = true
}

// 删除
async function handleDelete(row: Api.ShiftScheduleList.Response) {
  try {
    await ElMessageBox.confirm('确定要删除这条排班记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const api = DeleteShiftScheduleApi()
    await api.fetchData({ id: String(row.id), delete_remark: '删除排班记录' })
    if (api.success.value) {
      ElMessage.success('删除成功')
      getShiftScheduleList()
    }
    else {
      ElMessage.error(api.msg.value || '删除失败')
    }
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value?.validate()

    const api = form.id ? PutShiftScheduleUpdateApi() : PostShiftScheduleAddApi()
    await api.fetchData(form)

    if (api.success.value) {
      ElMessage.success(form.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getShiftScheduleList()
    }
    else {
      ElMessage.error(api.msg.value || '提交失败')
    }
  }
  catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

// 对话框关闭
function handleDialogClose() {
  formRef.value?.resetFields()
  resetForm()
}

// 重置表单
function resetForm() {
  Object.assign(form, {
    id: undefined,
    schedule_name: '',
    weaving_factory_id: undefined,
    start_time: '08:00',
    end_time: '17:00',
    early_clock_in_minutes: 0,
    remark: '',
  })
}

// 组件挂载
onMounted(() => {
  getShiftScheduleList()
})
</script>

<template>
  <div class="list-page">
    <!-- 操作栏 -->
    <FildCard title="" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="排班表名称:">
          <el-input
            v-model="searchForm.schedule_name"
            placeholder="请输入排班表名称"
            clearable
            @input="handleSearch"
          />
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂:">
          <SelectComponents
            v-model="searchForm.weaving_factory_id"
            :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
            api="GetBusinessUnitListApi"
            label-field="name"
            value-field="id"
            placeholder="请选择织厂"
            clearable
            @change-value="handleSearch"
          />
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <SelectComponents v-model="searchForm.status" style="width: 200px" api="StatusListApi" label-field="name" value-field="id" clearable />
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增排班
          </el-button>
          <!-- <el-button @click="handleBatchAdd">
            <el-icon><DocumentAdd /></el-icon>
            批量排班
          </el-button> -->
          <!-- <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button> -->
        </DescriptionsFormItem>
      </div>
    </FildCard>

    <!-- 排班表格 -->
    <FildCard title="排班列表" class="table-card-full" :tool-bar="false">
      <Table
        :config="tableConfig"
        :table-list="tableData"
        :column-list="columnList"
      >
        <template #status="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status_name || (row.status === 1 ? '启用' : '禁用') }}
          </el-tag>
        </template>
        <template #operate="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">
            编辑
          </el-button>
          <!-- <el-button type="info" link @click="handleDetail(row)">
            详情
          </el-button> -->
          <el-button type="danger" link @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </Table>
    </FildCard>

    <!-- 新增/编辑对话框 -->
    <vxe-modal
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      height="auto"
      :mask="true"
      :lock-view="true"
      :esc-closable="true"
      :resize="false"
      :show-footer="true"
      @close="handleDialogClose"
    >
      <template #default>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
        >
          <el-form-item label="排班表名称" prop="schedule_name">
            <el-input
              v-model="form.schedule_name"
              placeholder="请输入排班表名称"
            />
          </el-form-item>
          <el-form-item label="织厂" prop="weaving_factory_id">
            <SelectComponents
              v-model="form.weaving_factory_id"
              :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
              api="GetBusinessUnitListApi"
              label-field="name"
              value-field="id"
              placeholder="请选择织厂"
            />
          </el-form-item>
          <el-form-item label="开始时间" prop="start_time">
            <el-time-picker
              v-model="form.start_time"
              placeholder="选择开始时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="结束时间" prop="end_time">
            <el-time-picker
              v-model="form.end_time"
              placeholder="选择结束时间"
              format="HH:mm"
              value-format="HH:mm"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="允许提前打卡(分钟)" prop="early_clock_in_minutes">
            <el-input-number
              v-model="form.early_clock_in_minutes"
              :min="0"
              :precision="0"
              :max="60"
              placeholder="请输入允许提前打卡时间"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="handleSubmit">
            确定
          </el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
