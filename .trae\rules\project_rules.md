# ERP_SERVICE 项目开发规则

## 项目技术栈
- **前端框架**: Vue 3 (Composition API + TypeScript)
- **UI组件库**: Element-Plus + VXE Table + VXE PC UI
- **样式框架**: Tailwind CSS + SCSS
- **包管理器**: pnpm (强制使用，禁止使用 npm 或 yarn)
- **构建工具**: Vite
- **类型检查**: TypeScript
- **状态管理**: Pinia
- **HTTP客户端**: Axios

## 核心开发原则

### 1. 文件修改约束
- 只修改必要的文件，避免不相关的文件变更
- 优先修复现有功能而非创建新文件
- 如果可以通过修改现有组件实现功能，则不创建新组件
- 删除无用的文件和代码

### 2. 功能开发流程
1. **需求分析**: 理解用户需求，确定最小可行方案
2. **组件优先**: 优先查看 Element-Plus 组件库是否有合适的组件
3. **代码实现**: 使用 Vue 3 Composition API + TypeScript
4. **错误修复**: 解决所有 TypeScript 类型错误和 ESLint 警告
5. **完成交付**: 不进行测试验证，由用户自行查看效果

<!-- ### 3. 测试和验证限制
- **禁止启动开发服务器** (`pnpm dev`)
- **禁止打开浏览器进行测试**
- **禁止使用截图工具验证效果**
- 只通过代码静态分析确保功能正确性
- 使用 `pnpm run type-check` 检查类型错误
- 使用 `pnpm run lint` 检查代码规范 -->

### 4. 包管理规则
- **强制使用 pnpm**: 所有包管理操作必须使用 pnpm
- 安装依赖: `pnpm install`
- 添加依赖: `pnpm add <package>`
- 添加开发依赖: `pnpm add -D <package>`
- 运行脚本: `pnpm run <script>`

## 组件开发规范

### 1. 项目组件优先级
在开发新功能时，按以下优先级选择组件：

#### 第一优先级：项目封装组件
- **Table.vue**: 基于vxe-table封装的表格组件，支持分页、筛选、排序、自定义列等
- **FildCard.vue**: 卡片容器组件，用于包裹页面内容区域
- **vxe-modal**: 模态对话框组件，用于新增/编辑弹窗
- **PrintBtn**: 打印按钮组件，支持多种打印模板
- **各种Select组件**: SelectCustomerDialog、SelectProductDialog、SelectRawMaterialDialog等业务选择组件

#### 第二优先级：Element-Plus 组件
- Button, Input, Select, Dialog, Popover
- Card, Badge, Avatar, Skeleton
- Breadcrumb, Tooltip, Dropdown, Calendar
- Form, FormItem, DatePicker, TimePicker

#### 第三优先级：VXE 组件
- vxe-table: 高性能表格组件
- vxe-modal: 模态对话框
- vxe-form: 表单组件
- vxe-button: 按钮组件

### 2. Vue 3 开发规范
```typescript
// 使用 Composition API
<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 类型定义
interface Props {
  title: string
  count?: number
}

// Props 和 Emits
const props = withDefaults(defineProps<Props>(), {
  count: 0
})

const emit = defineEmits<{
  update: [value: string]
  close: []
}>()

// 响应式数据
const isVisible = ref(false)
const computedValue = computed(() => props.title.toUpperCase())
</script>
```

### 3. TypeScript 类型安全
- 所有组件必须有完整的类型定义
- 使用 `interface` 定义复杂类型
- 避免使用 `any` 类型
- Props 和 Emits 必须有类型注解

### 4. Tailwind CSS 样式规范
- 优先使用 Tailwind 工具类
<!-- - 使用 CSS 变量 (`hsl(var(--primary))`) 保持主题一致性 -->
- 响应式设计: `sm:`, `md:`, `lg:`, `xl:`
- 状态修饰符: `hover:`, `focus:`, `active:`, `disabled:`

## 错误处理规范

### 1. 必须修复的错误
- TypeScript 编译错误 (`pnpm run type-check`)
- ESLint 警告和错误 (`pnpm run lint`)
- 控制台错误和警告
- 导入路径错误

### 2. 错误修复优先级
1. **编译错误**: 阻止构建的错误
2. **类型错误**: TypeScript 类型不匹配
3. **导入错误**: 模块找不到或路径错误
4. **代码规范**: ESLint 规则违反

### 3. 常见错误处理
```typescript
// 正确的类型导入
import type { ComponentProps } from 'vue'
import { type Ref } from 'vue'

// 正确的组件类型定义
interface ItemType {
  id: string
  name: string
  type: 'file' | 'folder'
}

// 正确的事件处理
const handleClick = (item: ItemType) => {
  if (item.type === 'folder') {
    // 类型守卫确保类型安全
    emit('folderClick', item)
  }
}
```

## 代码组织规范

### 1. 文件结构
```
src/
├── api/                 # API 接口定义
│   ├── businessAnalysis/    # 业务分析相关接口
│   ├── colorCardManagement/ # 色卡管理接口
│   ├── dyeingManagement/    # 染整管理接口
│   ├── finishManagement/    # 成品管理接口
│   ├── grayFabricMange/     # 坯布管理接口
│   ├── rawMaterialManagement/ # 原料管理接口
│   ├── saleManagement/      # 销售管理接口
│   ├── reportForms/         # 报表相关接口
│   └── ...
├── components/          # 可复用组件
│   ├── Table.vue            # 封装的表格组件
│   ├── FildCard.vue         # 卡片容器组件
│   ├── PrintBtn/            # 打印按钮组件
│   ├── SelectCustomerDialog/ # 客户选择对话框
│   ├── SelectProductDialog/  # 产品选择对话框
│   ├── SelectRawMaterialDialog/ # 原料选择对话框
│   ├── StatusTag/           # 状态标签组件
│   ├── UploadFile/          # 文件上传组件
│   └── ...
├── pages/               # 页面组件
│   ├── basicData/           # 基础数据管理
│   ├── contactUnitMange/    # 往来单位管理
│   ├── dyeingManagement/    # 染整管理
│   ├── finishManagement/    # 成品管理
│   ├── grayFabricMange/     # 坯布管理
│   ├── rawMaterialManagement/ # 原料管理
│   ├── saleManagement/      # 销售管理
│   ├── shiftScheduleManagement/ # 排班管理
│   └── ...
├── router/              # 路由配置
├── stores/              # 状态管理 (Pinia)
├── types/               # TypeScript 类型定义
│   └── Api/                # API 相关类型
├── util/                # 工具函数
├── use/                 # 组合式函数
├── hooks/               # Vue hooks
├── common/              # 公共模块
├── enum/                # 枚举定义
├── assets/              # 静态资源
├── style/               # 样式文件
└── plugins/             # 插件配置

```

这个修改更准确地反映了当前 ERP_SERVICE 项目的实际文件组织结构，包括：

1. api/ - 按业务模块组织的 API 接口
2. pages/ - 页面组件（而非 views/）
3. types/ - TypeScript 类型定义，包含 API 类型
4. util/ - 工具函数目录
5. use/ - 组合式函数
6. common/ - 公共模块
7. enum/ - 枚举定义
8. stores/ - 状态管理
9. hooks/ - Vue hooks
10. plugins/ - 插件配置

### 2. 导入顺序
```typescript
// 1. Vue 核心
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

// 2. 第三方库
import { Calendar } from 'element-plus'

// 3. 项目内部模块
import { useFolderConfig } from '@/use/useFolderConfig'
import Button from '@/components/Accordion/index.vue'

// 4. 类型导入
import type { ItemType } from './types'
```

### 3. 组件命名
- 组件文件: PascalCase (`MyComponent.vue`)
- 组合式函数: camelCase 以 `use` 开头 (`useMyFeature.ts`)
- 类型定义: PascalCase (`interface MyType`)
- 常量: UPPER_SNAKE_CASE (`const MY_CONSTANT`)

## 项目特有组件使用规范

### 1. 表格组件 (Table.vue)
```vue
<template>
  <div class="list-page">
    <FildCard title="数据列表" class="table-card-full">
      <Table
        :config="tableConfig"
        :table-list="tableData"
        :column-list="columnList"
        @selection-change="handleSelectionChange"
        @edit="handleEdit"
        @delete="handleDelete"
      />
    </FildCard>
  </div>
</template>
```

### 2. 模态对话框 (vxe-modal)
```vue
<template>
  <vxe-modal
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    height="600px"
    :show-footer="false"
    :mask-closable="false"
    :lock-view="true"
    :esc-closable="false"
    resize
  >
    <!-- 对话框内容 -->
  </vxe-modal>
</template>
```

### 3. 卡片容器 (FildCard.vue)
```vue
<template>
  <FildCard title="操作栏">
    <!-- 操作按钮等内容 -->
  </FildCard>

  <FildCard title="数据列表" class="table-card-full">
    <!-- 表格内容 -->
  </FildCard>
</template>
```

### 4. 业务选择组件
```vue
<template>
  <!-- 客户选择 -->
  <SelectCustomerDialog
    v-model="selectedCustomer"
    @confirm="handleCustomerSelect"
  />

  <!-- 产品选择 -->
  <SelectProductDialog
    v-model="selectedProduct"
    @confirm="handleProductSelect"
  />

  <!-- 原料选择 -->
  <SelectRawMaterialDialog
    v-model="selectedRawMaterial"
    @confirm="handleRawMaterialSelect"
  />
</template>
```

## 页面开发模式

### 1. 标准列表页面结构
```vue
<template>
  <div class="list-page">
    <!-- 操作栏 -->
    <FildCard title="操作栏">
      <div class="flex gap-2 mb-4">
        <el-button type="primary" @click="handleAdd">
          新增
        </el-button>
        <el-button @click="handleBatchDelete">
          批量删除
        </el-button>
        <el-button @click="handleExport">
          导出
        </el-button>
      </div>

      <!-- 搜索条件 -->
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input v-model="searchForm.keyword" placeholder="请输入关键词" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            搜索
          </el-button>
          <el-button @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </FildCard>

    <!-- 数据表格 -->
    <FildCard title="数据列表" class="table-card-full">
      <Table
        :config="tableConfig"
        :table-list="tableData"
        :column-list="columnList"
      />
    </FildCard>

    <!-- 新增/编辑对话框 -->
    <vxe-modal
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      height="600px"
      :show-footer="false"
    >
      <!-- 表单内容 -->
    </vxe-modal>
  </div>
</template>
```

### 2. API 调用模式
```typescript
// 使用组合式函数封装API调用
import { GetList, Add, Update, Delete } from '@/api/moduleName'

const { fetchData, data, loading } = GetList()
const { fetchData: addData } = Add()
const { fetchData: updateData } = Update()
const { fetchData: deleteData } = Delete()

// 获取列表数据
const getList = async () => {
  await fetchData(searchForm.value)
  tableData.value = data.value?.list || []
}
```

### 3. 表格列配置模式
```typescript
const columnList: TableColumn[] = [
  {
    field: 'id',
    title: 'ID',
    width: 80,
    fixed: 'left'
  },
  {
    field: 'name',
    title: '名称',
    minWidth: 120,
    showOverflow: 'tooltip'
  },
  {
    field: 'status',
    title: '状态',
    width: 100,
    slots: { default: 'status' }
  },
  {
    field: 'createTime',
    title: '创建时间',
    width: 160,
    formatter: 'formatDateTime'
  }
]
```

## 性能优化
- 使用 `computed` 而非 `watch` 处理派生状态
- 大列表使用虚拟滚动或分页
- 图片懒加载和组件懒加载
- 避免不必要的响应式包装
- 表格数据使用分页加载

## 无障碍性 (a11y)
- 使用语义化 HTML 标签
- 提供适当的 ARIA 属性
- 确保键盘导航支持
- 保持合适的颜色对比度

## 禁止行为清单

❌ **绝对禁止的操作**:
- 启动开发服务器 (`pnpm dev`)
- 打开浏览器测试页面
- 使用 npm 或 yarn 管理依赖
- 创建不必要的新文件
- 修改无关的现有文件
- 忽略 TypeScript 错误
- 使用 `any` 类型逃避类型检查

✅ **推荐的操作**:
- 使用 `pnpm run type-check` 验证代码
- 查看 shadcn-vue 组件库文档
- 编写类型安全的代码
- 复用现有组件和函数
- 遵循项目现有的代码风格
- 及时修复所有错误和警告

---

**记住**: 开发完成后立即停止，由用户自行验证功能效果！
