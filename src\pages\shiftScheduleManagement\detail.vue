<template>
  <div class="shift-schedule-detail p-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/shiftScheduleManagement/schedule' }">排班管理</el-breadcrumb-item>
        <el-breadcrumb-item>排班详情</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex justify-between items-center mt-4">
        <h1 class="text-2xl font-bold text-gray-800">排班详情</h1>
        <div class="space-x-2">
          <el-button type="primary" @click="handleEdit">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button @click="handleBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </div>
      </div>
    </div>

    <!-- 详情内容 -->
    <div class="bg-white rounded-lg shadow-sm" v-loading="loading">
      <!-- 基本信息 -->
      <div class="p-6 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">基本信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div class="detail-item">
            <label class="detail-label">班次</label>
            <div class="detail-value">
              <el-tag :type="detail.shiftType === 'morning' ? 'success' : 'warning'" size="large">
                <el-icon class="mr-1">
                  <Sunrise v-if="detail.shiftType === 'morning'" />
                  <Moon v-else />
                </el-icon>
                {{ detail.shiftType === 'morning' ? '早班' : '晚班' }}
              </el-tag>
            </div>
          </div>
          <div class="detail-item">
            <label class="detail-label">状态</label>
            <div class="detail-value">
              <el-tag :type="getStatusType(detail.status)" size="large">
                {{ getStatusText(detail.status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 工作时间信息 -->
      <div class="p-6 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">工作时间</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="detail-item">
            <label class="detail-label">开始时间</label>
            <div class="detail-value text-lg font-medium text-green-600">
              <el-icon class="mr-1"><Clock /></el-icon>
              {{ detail.startTime }}
            </div>
          </div>
          <div class="detail-item">
            <label class="detail-label">结束时间</label>
            <div class="detail-value text-lg font-medium text-red-600">
              <el-icon class="mr-1"><Clock /></el-icon>
              {{ detail.endTime }}
            </div>
          </div>
          <div class="detail-item">
            <label class="detail-label">工作时长</label>
            <div class="detail-value text-lg font-medium text-blue-600">
              <el-icon class="mr-1"><Timer /></el-icon>
              {{ workDuration }}
            </div>
          </div>
          <div class="detail-item">
            <label class="detail-label">工作地点</label>
            <div class="detail-value">
              <el-icon class="mr-1"><Location /></el-icon>
              {{ detail.workLocation || '-' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 管理信息 -->
      <div class="p-6 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">管理信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div class="detail-item">
            <label class="detail-label">负责人</label>
            <div class="detail-value">
              <el-avatar :size="24" class="mr-2" v-if="detail.supervisorName">
                {{ detail.supervisorName?.charAt(0) }}
              </el-avatar>
              {{ detail.supervisorName || '-' }}
            </div>
          </div>
          <div class="detail-item">
            <label class="detail-label">创建时间</label>
            <div class="detail-value">
              <el-icon class="mr-1"><Clock /></el-icon>
              {{ detail.createTime }}
            </div>
          </div>
          <div class="detail-item">
            <label class="detail-label">更新时间</label>
            <div class="detail-value">
              <el-icon class="mr-1"><Clock /></el-icon>
              {{ detail.updateTime || '-' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="p-6">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">备注信息</h2>
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="text-gray-600">
            {{ detail.remark || '暂无备注' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 操作历史 -->
    <div class="bg-white rounded-lg shadow-sm mt-6" v-if="operationHistory.length > 0">
      <div class="p-6">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">操作历史</h2>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in operationHistory"
            :key="index"
            :timestamp="item.time"
            placement="top"
          >
            <div class="flex items-center">
              <el-tag :type="getOperationType(item.type)" size="small" class="mr-2">
                {{ item.type }}
              </el-tag>
              <span class="text-gray-600">{{ item.description }}</span>
              <span class="text-gray-400 ml-2">by {{ item.operator }}</span>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Edit,
  ArrowLeft,
  Clock,
  Timer,
  Location,
  Sunrise,
  Moon
} from '@element-plus/icons-vue'
import { GetShiftScheduleDetailApi } from '@/api/shiftSchedule'

// 路由
const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const detail = reactive({
  id: '',
  shiftType: '',
  startTime: '',
  endTime: '',
  workLocation: '',
  supervisorName: '',
  status: '',
  remark: '',
  createTime: '',
  updateTime: ''
})

// 操作历史（模拟数据）
const operationHistory = ref([
  {
    type: '创建',
    description: '创建排班记录',
    operator: '管理员',
    time: '2024-01-15 10:30:00'
  },
  {
    type: '修改',
    description: '修改工作时间',
    operator: '张三',
    time: '2024-01-16 14:20:00'
  }
])

// 计算工作时长
const workDuration = computed(() => {
  if (!detail.startTime || !detail.endTime) return '-'
  
  const start = new Date(`2000-01-01 ${detail.startTime}`)
  const end = new Date(`2000-01-01 ${detail.endTime}`)
  
  if (end <= start) {
    // 跨天情况
    end.setDate(end.getDate() + 1)
  }
  
  const diff = end.getTime() - start.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
})

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    completed: 'warning'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '正常',
    inactive: '停用',
    completed: '已完成'
  }
  return statusMap[status] || '未知'
}

// 获取操作类型
const getOperationType = (type: string) => {
  const typeMap: Record<string, string> = {
    创建: 'success',
    修改: 'warning',
    删除: 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取排班详情
const getShiftScheduleDetail = async () => {
  const id = route.params.id as string
  if (!id) {
    ElMessage.error('缺少排班ID')
    router.push('/shiftScheduleManagement/schedule')
    return
  }

  loading.value = true
  try {
    const { request } = GetShiftScheduleDetailApi()
    const response = await request({ id })
    if (response.code === 200) {
      const data = response.data
      Object.assign(detail, {
        id: data.id,
        shiftType: data.shiftType,
        startTime: data.startTime,
        endTime: data.endTime,
        workLocation: data.workLocation || '',
        supervisorName: data.supervisorName || '',
        status: data.status || 'active',
        remark: data.remark || '',
        createTime: data.createTime || '',
        updateTime: data.updateTime || ''
      })
    } else {
      ElMessage.error(response.message || '获取排班详情失败')
    }
  } catch (error) {
    console.error('获取排班详情失败:', error)
    ElMessage.error('获取排班详情失败')
  } finally {
    loading.value = false
  }
}

// 编辑
const handleEdit = () => {
  router.push(`/shiftScheduleManagement/schedule/edit/${detail.id}`)
}

// 返回
const handleBack = () => {
  router.push('/shiftScheduleManagement/schedule')
}

// 组件挂载
onMounted(() => {
  getShiftScheduleDetail()
})
</script>

<style scoped>
.shift-schedule-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.detail-item {
  @apply flex flex-col;
}

.detail-label {
  @apply text-sm font-medium text-gray-500 mb-1;
}

.detail-value {
  @apply text-base text-gray-900 flex items-center;
}

:deep(.el-timeline-item__timestamp) {
  color: #6b7280;
  font-size: 12px;
}
</style>