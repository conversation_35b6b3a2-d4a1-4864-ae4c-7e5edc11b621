import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 排班列表
export function GetShiftScheduleListApi() {
  return useRequest<Api.ShiftScheduleList.Request, Api.ShiftScheduleList.Response>({
    url: '/admin/v1/basic_data/weavingFactorySchedule/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 排班列表--导出
// export function GetShiftScheduleListApiExport({ nameFile }: any) {
//   return useDownLoad({
//     url: '/admin/v1/shiftSchedule/list',
//     method: 'get',
//     nameFileTime: false,
//     nameFile,
//   })
// }

// 删除排班
export function DeleteShiftScheduleApi() {
  return useRequest<Api.DeleteShiftScheduleApi.Request, Api.DeleteShiftScheduleApi.Response>({
    url: '/admin/v1/basic_data/weavingFactorySchedule',
    method: 'delete',
  })
}

// 更改排班状态
export function DisableShiftScheduleApi() {
  return useRequest<Api.DisableShiftScheduleApi.Request, Api.DisableShiftScheduleApi.Response>({
    url: '/admin/v1/basic_data/weavingFactorySchedule/updateStatus',
    method: 'put',
  })
}

// 新建排班
export function PostShiftScheduleAddApi() {
  return useRequest<Api.PostShiftScheduleAddApi.Request, Api.PostShiftScheduleAddApi.Response>({
    url: '/admin/v1/basic_data/weavingFactorySchedule/add',
    method: 'post',
  })
}

// 更新排班
export function PutShiftScheduleUpdateApi() {
  return useRequest<Api.PutShiftScheduleUpdateApi.Request, Api.PutShiftScheduleUpdateApi.Response>({
    url: '/admin/v1/basic_data/weavingFactorySchedule/update',
    method: 'put',
  })
}

// 排班详情
export function GetShiftScheduleDetailApi() {
  return useRequest<Api.GetShiftScheduleDetailApi.Request, Api.GetShiftScheduleDetailApi.Response>({
    url: '/admin/v1/basic_data/weavingFactorySchedule/get',
    method: 'get',
  })
}

// 获取班次类型列表
export function GetShiftTypeListApi() {
  return useRequest({
    url: '/admin/v1/shiftSchedule/shiftType/list',
    method: 'get',
  })
}

// 批量创建排班
// export function PostBatchShiftScheduleApi() {
//   return useRequest({
//     url: '/admin/v1/shiftSchedule/batch',
//     method: 'post',
//   })
// }

// // 获取排班统计
// export function GetShiftScheduleStatisticsApi() {
//   return useRequest({
//     url: '/admin/v1/shiftSchedule/statistics',
//     method: 'get',
//   })
// }
